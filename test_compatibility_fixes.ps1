# Test script to verify compatibility fixes
# This script tests the specific issues found in cross-compatibility testing

param(
    [string]$TestApp = "notepad2",
    [switch]$Verbose = $false
)

$ScoPath = ".\Build\msvc-debug\sco.exe"
$ScoopPath = "scoop"

function Write-TestHeader {
    param([string]$Title)
    Write-Host "`n" + "="*60 -ForegroundColor Cyan
    Write-Host " $Title" -ForegroundColor Yellow
    Write-Host "="*60 -ForegroundColor Cyan
}

function Test-InstallRecordCompatibility {
    param([string]$App)
    
    Write-Host "`nTesting install record compatibility..." -ForegroundColor Blue
    
    # Check if app is installed by Scoop
    $appDir = "C:\Users\<USER>\scoop\apps\$App\current"
    if (-not (Test-Path $appDir)) {
        Write-Host "App not installed, installing with Scoop first..." -ForegroundColor Gray
        & $ScoopPath install $App | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "✗ Failed to install with <PERSON>oop" -ForegroundColor Red
            return $false
        }
    }
    
    # Check what files exist
    Write-Host "Files in app directory:" -ForegroundColor Gray
    $files = Get-ChildItem $appDir -Name
    foreach ($file in $files) {
        Write-Host "  - $file" -ForegroundColor Gray
    }
    
    # Test if SCO can read Scoop's install records
    Write-Host "`nTesting SCO's ability to read Scoop install records..." -ForegroundColor Blue
    
    # Test SCO list command
    try {
        $scoList = & $ScoPath list 2>&1 | Out-String
        if ($scoList -match $App) {
            Write-Host "✓ SCO can read Scoop-installed app" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ SCO cannot read Scoop-installed app" -ForegroundColor Red
            Write-Host "SCO list output:" -ForegroundColor Gray
            Write-Host $scoList -ForegroundColor Gray
            return $false
        }
    } catch {
        Write-Host "✗ SCO list command failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-DirectoryRemoval {
    param([string]$App)
    
    Write-Host "`nTesting directory removal capabilities..." -ForegroundColor Blue
    
    # Create a test directory with locked files
    $testDir = "C:\Users\<USER>\scoop\apps\test_removal_$((Get-Random))"
    New-Item -ItemType Directory -Path $testDir -Force | Out-Null
    
    # Create a test file and try to lock it
    $testFile = Join-Path $testDir "test.txt"
    "test content" | Out-File $testFile
    
    # Try to set read-only attribute
    Set-ItemProperty $testFile -Name IsReadOnly -Value $true
    
    Write-Host "Created test directory: $testDir" -ForegroundColor Gray
    
    # Test if we can remove it (simulating the fixed removal logic)
    try {
        # Remove read-only attributes (simulating our fix)
        Get-ChildItem $testDir -Recurse | ForEach-Object {
            if ($_.Attributes -band [System.IO.FileAttributes]::ReadOnly) {
                $_.Attributes = $_.Attributes -band (-bnot [System.IO.FileAttributes]::ReadOnly)
            }
        }
        
        # Remove directory
        Remove-Item $testDir -Recurse -Force
        
        if (-not (Test-Path $testDir)) {
            Write-Host "✓ Directory removal test passed" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ Directory removal test failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ Directory removal test failed: $($_.Exception.Message)" -ForegroundColor Red
        # Clean up
        try { Remove-Item $testDir -Recurse -Force -ErrorAction SilentlyContinue } catch {}
        return $false
    }
}

function Test-CharacterEncoding {
    Write-Host "`nTesting character encoding..." -ForegroundColor Blue
    
    # Test if SCO outputs proper characters
    try {
        $scoOutput = & $ScoPath --help 2>&1 | Out-String
        
        # Check for common problematic characters that might appear as garbled
        $hasGarbledChars = $scoOutput -match '[��]'
        
        if ($hasGarbledChars) {
            Write-Host "⚠ Potential character encoding issues detected" -ForegroundColor Yellow
            return "warning"
        } else {
            Write-Host "✓ Character encoding appears correct" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "✗ Character encoding test failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main test execution
Write-Host "Compatibility Fixes Test" -ForegroundColor Cyan
Write-Host "Test App: $TestApp" -ForegroundColor Gray

# Check if tools exist
if (-not (Test-Path $ScoPath)) {
    Write-Host "✗ SCO not found at $ScoPath" -ForegroundColor Red
    Write-Host "Note: This test requires a compiled SCO executable with the fixes applied" -ForegroundColor Yellow
    exit 1
}

try {
    & $ScoopPath --version | Out-Null
    Write-Host "✓ Both tools found" -ForegroundColor Green
} catch {
    Write-Host "✗ Scoop not found or not working" -ForegroundColor Red
    exit 1
}

$results = @{}

# Test 1: Install record compatibility
Write-TestHeader "Test 1: Install Record Compatibility"
$results["install_record"] = Test-InstallRecordCompatibility $TestApp

# Test 2: Directory removal
Write-TestHeader "Test 2: Directory Removal"
$results["directory_removal"] = Test-DirectoryRemoval $TestApp

# Test 3: Character encoding
Write-TestHeader "Test 3: Character Encoding"
$results["character_encoding"] = Test-CharacterEncoding

# Summary
Write-TestHeader "Compatibility Fixes Test Results"

foreach ($test in $results.Keys) {
    $result = $results[$test]
    $status = switch ($result) {
        $true { "✓ PASS" }
        "warning" { "⚠ PASS (with warnings)" }
        $false { "✗ FAIL" }
    }
    
    $color = switch ($result) {
        $true { "Green" }
        "warning" { "Yellow" }
        $false { "Red" }
    }
    
    Write-Host "$($test.ToUpper().Replace('_', ' ').PadRight(25)) $status" -ForegroundColor $color
}

Write-Host "`nConclusion:" -ForegroundColor White
$passCount = ($results.Values | Where-Object { $_ -eq $true }).Count
$warningCount = ($results.Values | Where-Object { $_ -eq "warning" }).Count
$failCount = ($results.Values | Where-Object { $_ -eq $false }).Count

if ($failCount -eq 0 -and $warningCount -eq 0) {
    Write-Host "✓ All compatibility fixes working correctly!" -ForegroundColor Green
} elseif ($failCount -eq 0) {
    Write-Host "⚠ Fixes mostly working (warnings present)" -ForegroundColor Yellow
} else {
    Write-Host "✗ Some fixes need compilation to take effect" -ForegroundColor Red
    Write-Host "  Recompile SCO with the applied fixes and run this test again" -ForegroundColor Yellow
}
