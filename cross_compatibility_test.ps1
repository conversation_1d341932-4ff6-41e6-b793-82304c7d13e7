# Cross-Compatibility Test between SCO and Scoop
# Tests installation and uninstallation across both tools

param(
    [string]$TestApp = "notepad2",
    [switch]$Verbose = $false
)

$ScoPath = ".\Build\msvc-debug\sco.exe"
$ScoopPath = "scoop"

function Write-TestHeader {
    param([string]$Title)
    Write-Host "`n" + "="*60 -ForegroundColor Cyan
    Write-Host " $Title" -ForegroundColor Yellow
    Write-Host "="*60 -ForegroundColor Cyan
}

function Test-CrossCompatibility {
    param(
        [string]$TestName,
        [string]$InstallTool,
        [string]$UninstallTool,
        [string]$App
    )
    
    Write-Host "`n[$TestName]" -ForegroundColor White
    Write-Host "Install with: $InstallTool" -ForegroundColor Gray
    Write-Host "Uninstall with: $UninstallTool" -ForegroundColor Gray
    
    # Clean up first
    Write-Host "`nCleaning up existing installation..." -ForegroundColor Gray
    try {
        & $ScoPath uninstall $App 2>&1 | Out-Null
        & $ScoopPath uninstall $App 2>&1 | Out-Null
    } catch {
        # Ignore cleanup errors
    }
    
    # Install with first tool
    Write-Host "`nInstalling $App with $InstallTool..." -ForegroundColor Blue
    try {
        if ($InstallTool -eq "sco") {
            $installResult = & $ScoPath install $App 2>&1
        } else {
            $installResult = & $ScoopPath install $App 2>&1
        }
        $installExitCode = $LASTEXITCODE
        
        if ($Verbose) {
            Write-Host $installResult
        }
        
        if ($installExitCode -eq 0) {
            Write-Host "✓ Installation successful" -ForegroundColor Green
        } else {
            Write-Host "✗ Installation failed (Exit code: $installExitCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ Installation error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    # Check installation files
    Write-Host "`nChecking installation files..." -ForegroundColor Gray
    $appDir = "C:\Users\<USER>\scoop\apps\$App\current"
    if (Test-Path $appDir) {
        $files = Get-ChildItem $appDir -Name
        Write-Host "Files in app directory: $($files -join ', ')" -ForegroundColor Gray
        
        # Check for SCO vs Scoop install records
        $scoRecord = Test-Path "$appDir\scoop-install.json"
        $scoopInstall = Test-Path "$appDir\install.json"
        $scoopManifest = Test-Path "$appDir\manifest.json"
        
        Write-Host "SCO record (scoop-install.json): $scoRecord" -ForegroundColor Gray
        Write-Host "Scoop install record (install.json): $scoopInstall" -ForegroundColor Gray
        Write-Host "Scoop manifest (manifest.json): $scoopManifest" -ForegroundColor Gray
    } else {
        Write-Host "✗ App directory not found: $appDir" -ForegroundColor Red
        return $false
    }
    
    # Check if both tools can see the app
    Write-Host "`nChecking app visibility..." -ForegroundColor Gray
    
    # Check SCO list
    try {
        $scoList = & $ScoPath list 2>&1 | Out-String
        if ($scoList -match $App) {
            Write-Host "✓ SCO can see the app" -ForegroundColor Green
        } else {
            Write-Host "✗ SCO cannot see the app" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ SCO list failed" -ForegroundColor Red
    }
    
    # Check Scoop list
    try {
        $scoopList = & $ScoopPath list 2>&1 | Out-String
        if ($scoopList -match $App) {
            Write-Host "✓ Scoop can see the app" -ForegroundColor Green
        } else {
            Write-Host "✗ Scoop cannot see the app" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ Scoop list failed" -ForegroundColor Red
    }
    
    # Uninstall with second tool
    Write-Host "`nUninstalling $App with $UninstallTool..." -ForegroundColor Magenta
    try {
        if ($UninstallTool -eq "sco") {
            $uninstallResult = & $ScoPath uninstall $App 2>&1
        } else {
            $uninstallResult = & $ScoopPath uninstall $App 2>&1
        }
        $uninstallExitCode = $LASTEXITCODE
        
        if ($Verbose) {
            Write-Host $uninstallResult
        }
        
        # Check for warnings or errors in output
        $hasWarning = $uninstallResult -match "WARN|ERROR|isn't installed correctly"
        $hasError = $uninstallExitCode -ne 0
        
        if ($hasError) {
            Write-Host "✗ Uninstallation failed (Exit code: $uninstallExitCode)" -ForegroundColor Red
            return $false
        } elseif ($hasWarning) {
            Write-Host "⚠ Uninstallation succeeded with warnings" -ForegroundColor Yellow
            Write-Host "Warnings: $($uninstallResult | Where-Object { $_ -match 'WARN|ERROR' })" -ForegroundColor Yellow
            return "warning"
        } else {
            Write-Host "✓ Uninstallation successful" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "✗ Uninstallation error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main test execution
Write-Host "Cross-Compatibility Test: SCO vs Scoop" -ForegroundColor Cyan
Write-Host "Test App: $TestApp" -ForegroundColor Gray

# Check if tools exist
if (-not (Test-Path $ScoPath)) {
    Write-Host "✗ SCO not found at $ScoPath" -ForegroundColor Red
    exit 1
}

try {
    & $ScoopPath --version | Out-Null
    Write-Host "✓ Both tools found" -ForegroundColor Green
} catch {
    Write-Host "✗ Scoop not found or not working" -ForegroundColor Red
    exit 1
}

$results = @{}

# Test 1: SCO install → Scoop uninstall
Write-TestHeader "Test 1: SCO Install → Scoop Uninstall"
$results["sco_to_scoop"] = Test-CrossCompatibility "SCO→Scoop" "sco" "scoop" $TestApp

# Test 2: Scoop install → SCO uninstall
Write-TestHeader "Test 2: Scoop Install → SCO Uninstall"
$results["scoop_to_sco"] = Test-CrossCompatibility "Scoop→SCO" "scoop" "sco" $TestApp

# Summary
Write-TestHeader "Cross-Compatibility Test Results"

foreach ($test in $results.Keys) {
    $result = $results[$test]
    $status = switch ($result) {
        $true { "✓ PASS" }
        "warning" { "⚠ PASS (with warnings)" }
        $false { "✗ FAIL" }
    }
    
    $color = switch ($result) {
        $true { "Green" }
        "warning" { "Yellow" }
        $false { "Red" }
    }
    
    Write-Host "$($test.ToUpper().Replace('_', ' → ').PadRight(20)) $status" -ForegroundColor $color
}

Write-Host "`nConclusion:" -ForegroundColor White
$passCount = ($results.Values | Where-Object { $_ -eq $true }).Count
$warningCount = ($results.Values | Where-Object { $_ -eq "warning" }).Count
$failCount = ($results.Values | Where-Object { $_ -eq $false }).Count

if ($failCount -eq 0 -and $warningCount -eq 0) {
    Write-Host "✓ Full cross-compatibility achieved!" -ForegroundColor Green
} elseif ($failCount -eq 0) {
    Write-Host "⚠ Partial cross-compatibility (warnings present)" -ForegroundColor Yellow
    Write-Host "  Install record format differences cause warnings" -ForegroundColor Yellow
} else {
    Write-Host "✗ Cross-compatibility issues detected" -ForegroundColor Red
    Write-Host "  Install record format incompatibilities" -ForegroundColor Red
}

Write-Host "`nRecommendations:" -ForegroundColor White
Write-Host "  • Standardize install record format (install.json + manifest.json)" -ForegroundColor Gray
Write-Host "  • Ensure both tools can read each other's install records" -ForegroundColor Gray
Write-Host "  • Add migration tool for existing installations" -ForegroundColor Gray
