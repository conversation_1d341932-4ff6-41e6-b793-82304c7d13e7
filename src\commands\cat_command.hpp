#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/manifest.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <string>
#include <nlohmann/json.hpp>
#include "../utils/output.hpp"

namespace sco {

class CatCommand : public BaseCommand {
public:
    CatCommand() = default;
    
    int execute() override {
        try {
            if (app_name_.empty()) {
                std::cerr << "App name is required.\n";
                std::cout << "Usage: sco cat <app_name>\n";
                return 1;
            }

            output::debug("Showing manifest for app: {}", app_name_);

            return show_manifest();

        } catch (const std::exception& e) {
            output::error("Cat command failed: {}", e.what());
            std::cerr << "Failed to show manifest: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "cat"; }
    std::string get_description() const override { return "Show content of specified manifest"; }
    
    void set_app_name(const std::string& app_name) { app_name_ = app_name; }
    
private:
    std::string app_name_;
    
    int show_manifest() {
        // Find manifest file
        auto manifest_path = find_manifest_file(app_name_);
        if (manifest_path.empty()) {
            std::cout << "Manifest for '" << app_name_ << "' not found.\n";
            std::cout << "Try 'sco search " << app_name_ << "' to find similar apps.\n";
            return 1;
        }
        
        std::cout << "Manifest: " << manifest_path.string() << "\n";
        std::cout << std::string(80, '=') << "\n";
        
        // Read and display manifest content
        try {
            std::ifstream file(manifest_path);
            if (!file.is_open()) {
                std::cout << "Failed to open manifest file.\n";
                return 1;
            }
            
            // Read the entire file
            std::string content((std::istreambuf_iterator<char>(file)),
                               std::istreambuf_iterator<char>());
            file.close();
            
            // Try to parse and pretty-print JSON
            if (pretty_print_) {
                if (pretty_print_json(content)) {
                    return 0;
                }
                // Fall back to raw output if JSON parsing fails
            }
            
            // Raw output
            std::cout << content;
            if (!content.empty() && content.back() != '\n') {
                std::cout << '\n';
            }
            
            return 0;
            
        } catch (const std::exception& e) {
            std::cout << "Failed to read manifest file: " << e.what() << "\n";
            return 1;
        }
    }
    
    std::filesystem::path find_manifest_file(const std::string& app_name) {
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        
        if (!std::filesystem::exists(buckets_dir)) {
            output::debug("Buckets directory does not exist: {}", buckets_dir.string());
            return {};
        }
        
        try {
            // Search through all buckets
            for (const auto& bucket_entry : std::filesystem::directory_iterator(buckets_dir)) {
                if (bucket_entry.is_directory()) {
                    auto manifest_path = bucket_entry.path() / "bucket" / (app_name + ".json");
                    
                    if (std::filesystem::exists(manifest_path)) {
                        return manifest_path;
                    }
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            output::error("Filesystem error while searching for manifest: {}", e.what());
        }
        
        return {};
    }
    
    bool pretty_print_json(const std::string& content) {
        try {
            // Parse JSON
            auto json = nlohmann::json::parse(content);
            
            // Pretty print with indentation
            std::cout << json.dump(2) << std::endl;
            
            return true;
            
        } catch (const nlohmann::json::parse_error& e) {
            output::debug("Failed to parse JSON for pretty printing: {}", e.what());
            return false;
        }
    }
    
    void set_pretty_print(bool pretty) { pretty_print_ = pretty; }
    
private:
    bool pretty_print_ = true; // Default to pretty printing
};

} // namespace sco
