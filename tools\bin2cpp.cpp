#include <iostream>
#include <fstream>
#include <string>
#include <iomanip>

int main(int argc, char* argv[]) {
    if (argc != 4) {
        std::cerr << "Usage: " << argv[0] << " <input_file> <output_file> <variable_name>" << std::endl;
        return 1;
    }
    
    std::string input_file = argv[1];
    std::string output_file = argv[2];
    std::string variable_name = argv[3];
    
    std::ifstream input(input_file, std::ios::binary);
    if (!input) {
        std::cerr << "Error: Cannot open input file " << input_file << std::endl;
        return 1;
    }
    
    std::ofstream output(output_file);
    if (!output) {
        std::cerr << "Error: Cannot create output file " << output_file << std::endl;
        return 1;
    }
    
    // Get file size
    input.seekg(0, std::ios::end);
    size_t file_size = input.tellg();
    input.seekg(0, std::ios::beg);
    
    // Write header
    output << "// Auto-generated file - do not edit\n";
    output << "#include <cstddef>\n\n";
    output << "const unsigned char " << variable_name << "[] = {\n";
    
    // Write binary data
    char byte;
    size_t bytes_written = 0;
    while (input.read(&byte, 1)) {
        if (bytes_written % 16 == 0) {
            output << "    ";
        }
        
        output << "0x" << std::hex << std::setw(2) << std::setfill('0') 
               << (static_cast<unsigned char>(byte) & 0xFF);
        
        bytes_written++;
        if (bytes_written < file_size) {
            output << ",";
        }
        
        if (bytes_written % 16 == 0) {
            output << "\n";
        } else {
            output << " ";
        }
    }
    
    if (bytes_written % 16 != 0) {
        output << "\n";
    }
    
    output << "};\n";
    output << "const size_t " << variable_name << "_size = " << std::dec << file_size << ";\n\n";
    output << "// Force symbols to be exported\n";
    output << "extern \"C\" {\n";
    output << "    const unsigned char* get_" << variable_name << "() { return " << variable_name << "; }\n";
    output << "    size_t get_" << variable_name << "_size() { return " << variable_name << "_size; }\n";
    output << "}\n";
    
    std::cout << "Generated " << output_file << " with " << file_size << " bytes" << std::endl;
    return 0;
}
