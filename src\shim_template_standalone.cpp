#include <windows.h>
#include <shellapi.h>
#include <iostream>
#include <fstream>
#include <string>

#ifndef ERROR_ELEVATION_REQUIRED
#define ERROR_ELEVATION_REQUIRED 740
#endif

// Get the directory of the current executable
std::string get_exe_directory() {
    char path[MAX_PATH];
    GetModuleFileNameA(NULL, path, MAX_PATH);
    std::string exe_path(path);
    size_t last_slash = exe_path.find_last_of("\\/");
    if (last_slash != std::string::npos) {
        return exe_path.substr(0, last_slash);
    }
    return "";
}

// Get the name of the current executable without extension
std::string get_exe_name() {
    char path[MAX_PATH];
    GetModuleFileNameA(NULL, path, MAX_PATH);
    std::string exe_path(path);
    size_t last_slash = exe_path.find_last_of("\\/");
    size_t last_dot = exe_path.find_last_of(".");
    
    if (last_slash != std::string::npos && last_dot != std::string::npos && last_dot > last_slash) {
        return exe_path.substr(last_slash + 1, last_dot - last_slash - 1);
    }
    return "";
}

// Load shim configuration
bool load_shim_config(const std::string& config_path, std::string& target_path, std::string& args) {
    std::ifstream config_file(config_path);
    if (!config_file.is_open()) {
        return false;
    }
    
    std::string line;
    while (std::getline(config_file, line)) {
        size_t eq_pos = line.find('=');
        if (eq_pos == std::string::npos) continue;
        
        std::string key = line.substr(0, eq_pos);
        std::string value = line.substr(eq_pos + 1);
        
        if (key == "target") {
            target_path = value;
        } else if (key == "args") {
            args = value;
        }
    }
    
    return !target_path.empty();
}

// Get command line arguments
std::string get_command_line_args() {
    int argc;
    LPWSTR* argv = CommandLineToArgvW(GetCommandLineW(), &argc);
    
    if (argc <= 1) {
        LocalFree(argv);
        return "";
    }
    
    std::string result;
    for (int i = 1; i < argc; ++i) {
        if (i > 1) result += " ";
        
        // Convert wide string to narrow string
        int size = WideCharToMultiByte(CP_UTF8, 0, argv[i], -1, NULL, 0, NULL, NULL);
        std::string arg(size - 1, 0);
        WideCharToMultiByte(CP_UTF8, 0, argv[i], -1, &arg[0], size, NULL, NULL);
        
        // Quote arguments that contain spaces
        if (arg.find(' ') != std::string::npos) {
            result += "\"" + arg + "\"";
        } else {
            result += arg;
        }
    }
    
    LocalFree(argv);
    return result;
}

// Execute with elevation
int execute_with_elevation(const std::string& target_path, const std::string& args) {
    SHELLEXECUTEINFOA sei = {};
    sei.cbSize = sizeof(sei);
    sei.fMask = SEE_MASK_NOCLOSEPROCESS;
    sei.lpVerb = "runas";
    sei.lpFile = target_path.c_str();
    sei.lpParameters = args.empty() ? nullptr : args.c_str();
    sei.nShow = SW_SHOWNORMAL;
    
    if (!ShellExecuteExA(&sei)) {
        return static_cast<int>(GetLastError());
    }
    
    if (sei.hProcess) {
        WaitForSingleObject(sei.hProcess, INFINITE);
        
        DWORD exit_code = 0;
        GetExitCodeProcess(sei.hProcess, &exit_code);
        CloseHandle(sei.hProcess);
        
        return static_cast<int>(exit_code);
    }
    
    return 0;
}

int main() {
    // Get the directory and name of this executable
    std::string exe_dir = get_exe_directory();
    std::string exe_name = get_exe_name();
    
    if (exe_dir.empty() || exe_name.empty()) {
        return 1;
    }
    
    // Build config file path
    std::string config_path = exe_dir + "\\" + exe_name + ".shim";
    
    // Check if config file exists
    DWORD file_attr = GetFileAttributesA(config_path.c_str());
    if (file_attr == INVALID_FILE_ATTRIBUTES) {
        return 1;
    }
    
    // Load shim configuration
    std::string target_path;
    std::string add_args;
    if (!load_shim_config(config_path, target_path, add_args)) {
        return 1;
    }
    
    // Build command line
    std::string cmd_args = add_args;
    std::string pass_args = get_command_line_args();
    
    if (!pass_args.empty()) {
        if (!cmd_args.empty()) cmd_args += " ";
        cmd_args += pass_args;
    }
    
    if (!cmd_args.empty()) cmd_args = " " + cmd_args;
    std::string cmd = target_path + cmd_args;
    
    // Fix when GUI applications want to write to a console
    if (GetConsoleWindow() == nullptr) {
        AttachConsole(ATTACH_PARENT_PROCESS);
    }
    
    // Try to create process
    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    
    if (!CreateProcessA(nullptr, const_cast<char*>(cmd.c_str()), 
                       nullptr, nullptr, TRUE, 0, nullptr, nullptr, &si, &pi)) {
        
        DWORD error = GetLastError();
        if (error == ERROR_ELEVATION_REQUIRED) {
            // Use ShellExecute for elevation
            return execute_with_elevation(target_path, cmd_args);
        }
        return static_cast<int>(error);
    }
    
    // Wait for process to complete
    WaitForSingleObject(pi.hProcess, INFINITE);
    
    DWORD exit_code = 0;
    GetExitCodeProcess(pi.hProcess, &exit_code);
    
    // Close handles
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);
    
    return static_cast<int>(exit_code);
}
