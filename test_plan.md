# Scoop vs SCO 兼容性测试计划

## 测试目标
验证 SCO 项目是否完全兼容 Scoop 的命令行接口和功能，确保用户可以无缝从 Scoop 迁移到 SCO。

## 测试环境
- Windows 系统
- 已安装 Scoop
- 已构建 SCO (Build/msvc-debug/sco.exe)
- PowerShell 5.0+

## 测试分类

### 1. 基础命令测试 (Basic Commands)

#### 1.1 帮助系统
- [ ] `scoop help` vs `sco help` - 显示主帮助
- [ ] `scoop --version` vs `sco --version` - 版本信息
- [ ] `scoop help <command>` vs `sco help <command>` - 特定命令帮助
- [ ] `scoop <command> --help` vs `sco <command> --help` - 命令行帮助

#### 1.2 无效输入处理
- [ ] `scoop invalidcommand` vs `sco invalidcommand` - 无效命令
- [ ] `scoop` vs `sco` - 无参数调用
- [ ] 错误的参数组合

### 2. 信息查询命令测试 (Information Commands)

#### 2.1 系统状态
- [ ] `scoop status` vs `sco status` - 系统状态
- [ ] `scoop checkup` vs `sco checkup` - 系统检查

#### 2.2 应用列表
- [ ] `scoop list` vs `sco list` - 已安装应用列表
- [ ] `scoop list --verbose` vs `sco list --verbose` - 详细列表
- [ ] `scoop list <pattern>` vs `sco list <pattern>` - 模式匹配

#### 2.3 搜索功能
- [ ] `scoop search <query>` vs `sco search <query>` - 搜索应用
- [ ] `scoop search --verbose <query>` vs `sco search --verbose <query>` - 详细搜索
- [ ] 搜索不存在的应用

#### 2.4 应用信息
- [ ] `scoop info <app>` vs `sco info <app>` - 应用详细信息
- [ ] `scoop info <nonexistent>` vs `sco info <nonexistent>` - 不存在应用
- [ ] `scoop home <app>` vs `sco home <app>` - 打开应用主页

#### 2.5 路径和定位
- [ ] `scoop which <command>` vs `sco which <command>` - 定位可执行文件
- [ ] `scoop prefix <app>` vs `sco prefix <app>` - 获取应用路径

### 3. 包管理命令测试 (Package Management)

#### 3.1 安装功能
- [ ] `scoop install <app>` vs `sco install <app>` - 基本安装
- [ ] `scoop install --global <app>` vs `sco install --global <app>` - 全局安装
- [ ] `scoop install --skip <app>` vs `sco install --skip <app>` - 跳过检查
- [ ] 安装不存在的应用
- [ ] 安装已安装的应用

#### 3.2 卸载功能
- [ ] `scoop uninstall <app>` vs `sco uninstall <app>` - 基本卸载
- [ ] `scoop uninstall --global <app>` vs `sco uninstall --global <app>` - 全局卸载
- [ ] `scoop uninstall --purge <app>` vs `sco uninstall --purge <app>` - 完全卸载
- [ ] 卸载不存在的应用

#### 3.3 更新功能
- [ ] `scoop update` vs `sco update` - 更新所有
- [ ] `scoop update <app>` vs `sco update <app>` - 更新特定应用
- [ ] `scoop update --global` vs `sco update --global` - 全局更新
- [ ] `scoop update --force <app>` vs `sco update --force <app>` - 强制更新

#### 3.4 清理功能
- [ ] `scoop cleanup <app>` vs `sco cleanup <app>` - 清理特定应用
- [ ] `scoop cleanup --all` vs `sco cleanup --all` - 清理所有
- [ ] `scoop cleanup --global` vs `sco cleanup --global` - 全局清理

### 4. Bucket 管理测试 (Bucket Management)

#### 4.1 Bucket 操作
- [ ] `scoop bucket list` vs `sco bucket list` - 列出bucket
- [ ] `scoop bucket known` vs `sco bucket known` - 已知bucket
- [ ] `scoop bucket add <name>` vs `sco bucket add <name>` - 添加bucket
- [ ] `scoop bucket remove <name>` vs `sco bucket remove <name>` - 删除bucket

### 5. Shim 管理测试 (Shim Management)

#### 5.1 Shim 操作
- [ ] `scoop shim list` vs `sco shim list` - 列出shim
- [ ] `scoop shim add <name> <target>` vs `sco shim add <name> <target>` - 添加shim
- [ ] `scoop shim remove <name>` vs `sco shim remove <name>` - 删除shim
- [ ] `scoop shim repair` vs `sco shim repair` - 修复shim

### 6. 配置管理测试 (Configuration)

#### 6.1 配置操作
- [ ] `scoop config` vs `sco config` - 列出配置
- [ ] `scoop config <key>` vs `sco config <key>` - 获取配置值
- [ ] `scoop config <key> <value>` vs `sco config <key> <value>` - 设置配置
- [ ] `scoop config rm <key>` vs `sco config rm <key>` - 删除配置

#### 6.2 缓存管理
- [ ] `scoop cache` vs `sco cache` - 显示缓存
- [ ] `scoop cache show` vs `sco cache show` - 缓存详情
- [ ] `scoop cache rm <app>` vs `sco cache rm <app>` - 删除缓存
- [ ] `scoop cache rm --all` vs `sco cache rm --all` - 清空缓存

### 7. 高级功能测试 (Advanced Features)

#### 7.1 依赖管理
- [ ] `scoop depends <app>` vs `sco depends <app>` - 查看依赖
- [ ] `scoop depends --tree <app>` vs `sco depends --tree <app>` - 依赖树

#### 7.2 导出导入
- [ ] `scoop export` vs `sco export` - 导出配置
- [ ] `scoop export --config` vs `sco export --config` - 导出包含配置
- [ ] `scoop import <file>` vs `sco import <file>` - 导入配置

#### 7.3 版本锁定
- [ ] `scoop hold <app>` vs `sco hold <app>` - 锁定版本
- [ ] `scoop unhold <app>` vs `sco unhold <app>` - 解锁版本

#### 7.4 其他功能
- [ ] `scoop reset <app>` vs `sco reset <app>` - 重置应用
- [ ] `scoop cat <app>` vs `sco cat <app>` - 显示manifest
- [ ] `scoop download <app>` vs `sco download <app>` - 下载应用
- [ ] `scoop create <app>` vs `sco create <app>` - 创建manifest

### 8. 错误处理和边界情况测试

#### 8.1 参数错误
- [ ] 缺少必需参数
- [ ] 无效的参数组合
- [ ] 超长的参数

#### 8.2 权限错误
- [ ] 全局安装权限不足
- [ ] 文件系统权限问题

#### 8.3 网络错误
- [ ] 网络连接失败
- [ ] 下载中断

#### 8.4 文件系统错误
- [ ] 磁盘空间不足
- [ ] 路径过长
- [ ] 文件被占用

## 测试执行方法

### 自动化测试
使用提供的 PowerShell 脚本：
```powershell
# 运行所有测试
.\test_compatibility.ps1

# 运行特定分类测试
.\test_compatibility.ps1 -TestCategory basic
.\test_compatibility.ps1 -TestCategory info
.\test_compatibility.ps1 -TestCategory config

# 保存详细输出
.\test_compatibility.ps1 -SaveOutput -Verbose
```

### 手动测试
对于需要交互或特殊环境的测试，需要手动执行并记录结果。

## 测试结果评估标准

### 兼容性等级
1. **完全兼容** - 输出格式、退出码、行为完全一致
2. **功能兼容** - 核心功能一致，输出格式可能略有差异
3. **部分兼容** - 主要功能可用，但有明显差异
4. **不兼容** - 功能缺失或行为完全不同

### 关键指标
- 命令退出码一致性
- 输出格式相似性
- 错误处理一致性
- 功能完整性

## 测试报告
测试完成后，将生成详细的兼容性报告，包括：
- 每个命令的兼容性评级
- 发现的问题和差异
- 改进建议
- 优先级排序的修复计划
