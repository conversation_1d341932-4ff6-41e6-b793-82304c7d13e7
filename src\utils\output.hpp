#pragma once

#include <iostream>
#include <string>
#include <fmt/format.h>

#ifdef _WIN32
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifndef NOMINMAX
#define NOMINMAX
#endif
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

namespace sco {
namespace output {

// Global flags for controlling output
inline bool verbose = false;
inline bool quiet = false;

// Initialize output system with Unicode support
inline void init(bool verbose_mode = false, bool quiet_mode = false) {
    verbose = verbose_mode;
    quiet = quiet_mode;

#ifdef _WIN32
    // Enable UTF-8 output on Windows
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // Enable ANSI escape sequences for better Unicode support
    HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
    if (hOut != INVALID_HANDLE_VALUE) {
        DWORD dwMode = 0;
        if (GetConsoleMode(hOut, &dwMode)) {
            dwMode |= ENABLE_VIRTUAL_TERMINAL_PROCESSING;
            SetConsoleMode(hOut, dwMode);
        }
    }
#endif
}

// Output functions with fmt::format support
template<typename... Args>
inline void info(const std::string& format, Args&&... args) {
    if (!quiet) {
        if constexpr (sizeof...(args) == 0) {
            std::cout << format << std::endl;
        } else {
            std::cout << fmt::format(fmt::runtime(format), std::forward<Args>(args)...) << std::endl;
        }
    }
}

template<typename... Args>
inline void warn(const std::string& format, Args&&... args) {
    if (!quiet) {
        if constexpr (sizeof...(args) == 0) {
            std::cout << "⚠ " << format << std::endl;
        } else {
            std::cout << "⚠ " << fmt::format(fmt::runtime(format), std::forward<Args>(args)...) << std::endl;
        }
    }
}

template<typename... Args>
inline void error(const std::string& format, Args&&... args) {
    if constexpr (sizeof...(args) == 0) {
        std::cerr << "✗ " << format << std::endl;
    } else {
        std::cerr << "✗ " << fmt::format(fmt::runtime(format), std::forward<Args>(args)...) << std::endl;
    }
}

template<typename... Args>
inline void debug(const std::string& format, Args&&... args) {
    if (verbose && !quiet) {
        if constexpr (sizeof...(args) == 0) {
            std::cout << "[DEBUG] " << format << std::endl;
        } else {
            std::cout << "[DEBUG] " << fmt::format(fmt::runtime(format), std::forward<Args>(args)...) << std::endl;
        }
    }
}

template<typename... Args>
inline void success(const std::string& format, Args&&... args) {
    if (!quiet) {
        if constexpr (sizeof...(args) == 0) {
            std::cout << "✓ " << format << std::endl;
        } else {
            std::cout << "✓ " << fmt::format(format, std::forward<Args>(args)...) << std::endl;
        }
    }
}

} // namespace output
} // namespace sco
