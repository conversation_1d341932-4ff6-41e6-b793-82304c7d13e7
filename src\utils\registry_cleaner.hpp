#pragma once

#include <string>
#include <vector>
#include <filesystem>
#include "output.hpp"

#ifdef _WIN32
#include <windows.h>
#include <winreg.h>
#endif

namespace sco {

struct RegistryEntry {
    std::string hive;  // HKEY_CURRENT_USER, HKEY_LOCAL_MACHINE, etc.
    std::string key_path;
    std::string value_name;
    std::string value_data;
    std::string value_type;  // REG_SZ, REG_DWORD, etc.
};

class RegistryCleaner {
public:
    // Remove registry entries created during app installation
    static bool remove_app_registry_entries(const std::string& app_name) {
        bool success = true;
        
        output::info("Cleaning registry entries for app: {}", app_name);
        
        // Common registry locations where apps might create entries
        std::vector<std::string> common_paths = {
            "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\" + app_name,
            "Software\\Classes\\Applications\\" + app_name + ".exe",
            "Software\\" + app_name,
            "Software\\Classes\\." + app_name,
        };
        
        for (const auto& path : common_paths) {
            if (!remove_registry_key(HKEY_CURRENT_USER, path)) {
                output::debug("Failed to remove or key not found: HKCU\\{}", path);
                // Don't mark as failure since key might not exist
            }
        }
        
        return success;
    }
    
    // Remove specific registry entries from a list
    static bool remove_registry_entries(const std::vector<RegistryEntry>& entries) {
        bool all_success = true;
        
        for (const auto& entry : entries) {
            if (!remove_registry_entry(entry)) {
                all_success = false;
            }
        }
        
        return all_success;
    }
    
    // Remove registry entries created by Python installations (PEP 514)
    static bool remove_python_registry_entries(const std::string& app_name, const std::string& version) {
        bool success = true;
        
        output::info("Cleaning Python registry entries for: {} v{}", app_name, version);
        
#ifdef _WIN32
        // Python PEP 514 registry entries
        std::vector<std::string> python_paths = {
            "Software\\Python\\PythonCore\\" + version,
            "Software\\Python\\PythonCore\\" + version + "-32",
            "Software\\Python\\PythonCore\\" + version + "-64",
        };
        
        for (const auto& path : python_paths) {
            if (!remove_registry_key(HKEY_CURRENT_USER, path)) {
                output::debug("Failed to remove or key not found: HKCU\\{}", path);
            }
        }
#endif
        
        return success;
    }
    
    // Backup registry entries before removal (for safety)
    static bool backup_registry_entries(const std::vector<RegistryEntry>& entries, 
                                       const std::filesystem::path& backup_file) {
        try {
            std::ofstream backup(backup_file);
            if (!backup.is_open()) {
                output::error("Failed to create registry backup file: {}", backup_file.string());
                return false;
            }
            
            backup << "Windows Registry Editor Version 5.00\n\n";
            
            for (const auto& entry : entries) {
                // Write registry entry in .reg file format
                backup << "[" << entry.hive << "\\" << entry.key_path << "]\n";
                
                if (!entry.value_name.empty()) {
                    backup << "\"" << entry.value_name << "\"=";
                    
                    if (entry.value_type == "REG_SZ") {
                        backup << "\"" << entry.value_data << "\"\n";
                    } else if (entry.value_type == "REG_DWORD") {
                        backup << "dword:" << entry.value_data << "\n";
                    } else {
                        backup << "\"" << entry.value_data << "\"\n";
                    }
                }
                
                backup << "\n";
            }
            
            backup.close();
            output::info("Registry backup created: {}", backup_file.string());
            return true;

        } catch (const std::exception& e) {
            output::error("Failed to create registry backup: {}", e.what());
            return false;
        }
    }
    
    // Read registry entries that might have been created by an app
    static std::vector<RegistryEntry> scan_app_registry_entries(const std::string& app_name) {
        std::vector<RegistryEntry> entries;
        
#ifdef _WIN32
        // Scan common locations for app-related entries
        std::vector<std::pair<HKEY, std::string>> scan_locations = {
            {HKEY_CURRENT_USER, "Software"},
            {HKEY_CURRENT_USER, "Software\\Classes"},
            {HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall"},
        };
        
        for (const auto& [hkey, base_path] : scan_locations) {
            scan_registry_subtree(hkey, base_path, app_name, entries);
        }
#endif
        
        return entries;
    }
    
private:
#ifdef _WIN32
    static bool remove_registry_key(HKEY hkey, const std::string& key_path) {
        LONG result = RegDeleteTreeA(hkey, key_path.c_str());
        
        if (result == ERROR_SUCCESS) {
            output::info("Removed registry key: {}", key_path);
            return true;
        } else if (result == ERROR_FILE_NOT_FOUND) {
            output::debug("Registry key not found: {}", key_path);
            return true; // Not an error if key doesn't exist
        } else {
            output::warn("Failed to remove registry key {}: error {}", key_path, result);
            return false;
        }
    }
    
    static bool remove_registry_entry(const RegistryEntry& entry) {
        HKEY hkey = string_to_hkey(entry.hive);
        if (hkey == nullptr) {
            output::error("Invalid registry hive: {}", entry.hive);
            return false;
        }
        
        HKEY key_handle;
        LONG result = RegOpenKeyExA(hkey, entry.key_path.c_str(), 0, KEY_SET_VALUE, &key_handle);
        
        if (result != ERROR_SUCCESS) {
            output::debug("Failed to open registry key: {}\\{}", entry.hive, entry.key_path);
            return false;
        }
        
        if (entry.value_name.empty()) {
            // Remove the entire key
            RegCloseKey(key_handle);
            return remove_registry_key(hkey, entry.key_path);
        } else {
            // Remove specific value
            result = RegDeleteValueA(key_handle, entry.value_name.c_str());
            RegCloseKey(key_handle);
            
            if (result == ERROR_SUCCESS) {
                output::info("Removed registry value: {}\\{}\\{}",
                           entry.hive, entry.key_path, entry.value_name);
                return true;
            } else if (result == ERROR_FILE_NOT_FOUND) {
                output::debug("Registry value not found: {}\\{}\\{}",
                            entry.hive, entry.key_path, entry.value_name);
                return true;
            } else {
                output::warn("Failed to remove registry value: {}\\{}\\{}",
                           entry.hive, entry.key_path, entry.value_name);
                return false;
            }
        }
    }
    
    static HKEY string_to_hkey(const std::string& hive_name) {
        if (hive_name == "HKEY_CURRENT_USER" || hive_name == "HKCU") {
            return HKEY_CURRENT_USER;
        } else if (hive_name == "HKEY_LOCAL_MACHINE" || hive_name == "HKLM") {
            return HKEY_LOCAL_MACHINE;
        } else if (hive_name == "HKEY_CLASSES_ROOT" || hive_name == "HKCR") {
            return HKEY_CLASSES_ROOT;
        } else if (hive_name == "HKEY_USERS" || hive_name == "HKU") {
            return HKEY_USERS;
        } else if (hive_name == "HKEY_CURRENT_CONFIG" || hive_name == "HKCC") {
            return HKEY_CURRENT_CONFIG;
        }
        return nullptr;
    }
    
    static void scan_registry_subtree(HKEY hkey, const std::string& base_path, 
                                     const std::string& app_name, 
                                     std::vector<RegistryEntry>& entries) {
        HKEY key_handle;
        LONG result = RegOpenKeyExA(hkey, base_path.c_str(), 0, KEY_READ, &key_handle);
        
        if (result != ERROR_SUCCESS) {
            return;
        }
        
        // Enumerate subkeys
        DWORD index = 0;
        char subkey_name[256];
        DWORD subkey_name_size;
        
        while (true) {
            subkey_name_size = sizeof(subkey_name);
            result = RegEnumKeyExA(key_handle, index, subkey_name, &subkey_name_size,
                                  NULL, NULL, NULL, NULL);
            
            if (result != ERROR_SUCCESS) {
                break;
            }
            
            std::string subkey_str(subkey_name);
            
            // Check if subkey name contains app name (case insensitive)
            std::string subkey_lower = subkey_str;
            std::string app_name_lower = app_name;
            std::transform(subkey_lower.begin(), subkey_lower.end(), subkey_lower.begin(), ::tolower);
            std::transform(app_name_lower.begin(), app_name_lower.end(), app_name_lower.begin(), ::tolower);
            
            if (subkey_lower.find(app_name_lower) != std::string::npos) {
                RegistryEntry entry;
                entry.hive = hkey_to_string(hkey);
                entry.key_path = base_path + "\\" + subkey_str;
                entries.push_back(entry);
            }
            
            index++;
        }
        
        RegCloseKey(key_handle);
    }
    
    static std::string hkey_to_string(HKEY hkey) {
        if (hkey == HKEY_CURRENT_USER) {
            return "HKEY_CURRENT_USER";
        } else if (hkey == HKEY_LOCAL_MACHINE) {
            return "HKEY_LOCAL_MACHINE";
        } else if (hkey == HKEY_CLASSES_ROOT) {
            return "HKEY_CLASSES_ROOT";
        } else if (hkey == HKEY_USERS) {
            return "HKEY_USERS";
        } else if (hkey == HKEY_CURRENT_CONFIG) {
            return "HKEY_CURRENT_CONFIG";
        }
        return "UNKNOWN";
    }
#else
    // Non-Windows platforms don't have registry
    static bool remove_registry_key(void* hkey, const std::string& key_path) {
        output::debug("Registry operations not supported on non-Windows platforms");
        return true;
    }
    
    static bool remove_registry_entry(const RegistryEntry& entry) {
        output::debug("Registry operations not supported on non-Windows platforms");
        return true;
    }
    
    static void scan_registry_subtree(void* hkey, const std::string& base_path, 
                                     const std::string& app_name, 
                                     std::vector<RegistryEntry>& entries) {
        // No-op on non-Windows
    }
#endif

public:
    // Check if registry cleaning is supported on this platform
    static bool is_registry_cleaning_supported() {
#ifdef _WIN32
        return true;
#else
        return false;
#endif
    }
    
    // Get a list of registry entries that would be cleaned for an app
    static std::vector<std::string> get_cleanup_preview(const std::string& app_name) {
        std::vector<std::string> preview;
        
#ifdef _WIN32
        std::vector<std::string> common_paths = {
            "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\" + app_name,
            "HKCU\\Software\\Classes\\Applications\\" + app_name + ".exe",
            "HKCU\\Software\\" + app_name,
        };
        
        for (const auto& path : common_paths) {
            preview.push_back(path);
        }
#endif
        
        return preview;
    }
};

} // namespace sco
