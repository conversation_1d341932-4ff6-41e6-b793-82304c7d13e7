﻿
cmake_minimum_required(VERSION 3.15)

project(sco VERSION 0.1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

find_package(CLI11 CONFIG REQUIRED)
find_package(nlohmann_json CONFIG REQUIRED)
find_package(fmt CONFIG REQUIRED)

# Build bin2cpp tool first
add_executable(bin2cpp tools/bin2cpp.cpp)
if(MSVC)
    target_compile_options(bin2cpp PRIVATE /utf-8)
endif()

# Build shim template standalone executable
add_executable(shim_template_standalone src/shim_template_standalone.cpp)
if(WIN32)
    target_link_libraries(shim_template_standalone PRIVATE kernel32 user32)
endif()

# Generate embedded resource file
set(SHIM_TEMPLATE_RESOURCE "${CMAKE_CURRENT_BINARY_DIR}/shim_template_resource.cpp")
add_custom_command(
    OUTPUT ${SHIM_TEMPLATE_RESOURCE}
    COMMAND bin2cpp $<TARGET_FILE:shim_template_standalone> ${SHIM_TEMPLATE_RESOURCE} shim_template_data
    DEPENDS bin2cpp shim_template_standalone
    COMMENT "Generating embedded shim template resource"
    VERBATIM
)

# Collect all source files for main executable
file(GLOB_RECURSE SOURCES
    "src/*.cpp"
    "src/*.hpp"
)

# Exclude the standalone shim template and existing resource file from main build
list(FILTER SOURCES EXCLUDE REGEX ".*shim_template_standalone\\.cpp$")
list(FILTER SOURCES EXCLUDE REGEX ".*shim_template_resource\\.cpp$")

# Add the generated resource file to sources
list(APPEND SOURCES ${SHIM_TEMPLATE_RESOURCE})

add_executable(sco ${SOURCES})
target_link_directories(sco PRIVATE src)

target_link_libraries(sco PRIVATE
    CLI11::CLI11
    nlohmann_json::nlohmann_json
    fmt::fmt
)

if(MSVC)
    target_compile_options(sco PRIVATE /utf-8 /bigobj)
endif()

# Ensure the generated resource file is built before the main executable
add_dependencies(sco bin2cpp shim_template_standalone)