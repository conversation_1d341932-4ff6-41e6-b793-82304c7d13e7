#include "shim_manager.hpp"

#ifdef _WIN32
extern "C" {
    const unsigned char* get_shim_template_data();
    size_t get_shim_template_data_size();
}

namespace sco {

bool ShimManager::create_shim_executable_impl(const std::filesystem::path& shim_path) {
    try {
        // Get the embedded binary data
        const unsigned char* template_data = get_shim_template_data();
        size_t template_size = get_shim_template_data_size();

        // Write the embedded binary data to the shim file
        std::ofstream shim_file(shim_path, std::ios::binary);
        if (!shim_file.is_open()) {
            output::error("Failed to create shim file: " + shim_path.string());
            return false;
        }

        shim_file.write(reinterpret_cast<const char*>(template_data), template_size);
        shim_file.close();

        if (!shim_file.good()) {
            output::error("Failed to write shim data to: " + shim_path.string());
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        output::error("Failed to create shim executable: " + std::string(e.what()));
        return false;
    }
}

} // namespace sco

#endif
