#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../utils/shim_manager.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <algorithm>
#include "../utils/output.hpp"

namespace sco {

class ShimCommand : public BaseCommand {
public:
    ShimCommand() = default;
    
    int execute() override {
        try {
            if (action_.empty() || action_ == "list") {
                return list_shims();
            } else if (action_ == "add") {
                return add_shim();
            } else if (action_ == "remove" || action_ == "rm") {
                return remove_shim();
            } else if (action_ == "repair") {
                return repair_shims();
            } else {
                std::cout << "Unknown shim action: " << action_ << "\n";
                show_usage();
                return 1;
            }
        } catch (const std::exception& e) {
            output::error("Shim command failed: {}", e.what());
            std::cerr << "Shim command failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "shim"; }
    std::string get_description() const override { return "Manipulate Scoop shims"; }
    
    void set_action(const std::string& action) { action_ = action; }
    void set_shim_name(const std::string& name) { shim_name_ = name; }
    void set_target_path(const std::string& path) { target_path_ = path; }
    void set_command_args(const std::vector<std::string>& args) { command_args_ = args; }
    void set_global(bool global) { global_ = global; }
    
private:
    std::string action_;
    std::string shim_name_;
    std::string target_path_;
    std::vector<std::string> command_args_;
    bool global_ = false;
    
    void show_usage() {
        std::cout << "Usage:\n";
        std::cout << "  sco shim [list]                     # List all shims\n";
        std::cout << "  sco shim add <n> <target>        # Add a shim\n";
        std::cout << "  sco shim remove <n>              # Remove a shim\n";
        std::cout << "  sco shim repair                     # Repair all shims\n";
    }
    
    int list_shims() {
        auto shims = ShimManager::list_shims();

        if (shims.empty()) {
            std::cout << "No shims are currently installed.\n";
            return 0;
        }

        // Use Scoop-compatible format
        TableFormatter table;
        table.add_column("Name", true);
        table.add_column("Source", true);
        table.add_column("Alternatives", true);
        table.add_column("IsGlobal", true);
        table.add_column("IsHidden", true);

        for (const auto& shim : shims) {
            auto target = shim.target_path.string();
            auto source = get_shim_source(target);

            table.add_row({
                shim.name,
                source,
                "", // Alternatives - not implemented yet
                "False", // IsGlobal - could be determined from path
                "False"  // IsHidden - not implemented yet
            });
        }

        table.print();
        return 0;
    }
    
    int add_shim() {
        if (shim_name_.empty() || target_path_.empty()) {
            output::error("<command_path> must be specified for subcommand 'add'");
            show_usage();
            return 1;
        }
        
        std::string command_path = target_path_;
        
        // If command_path doesn't contain path separators, try to resolve it
        if (command_path.find('/') == std::string::npos && command_path.find('\\') == std::string::npos) {
            std::string short_path = command_path;
            
            // First try to get existing shim target
            command_path = get_shim_target_path(short_path);
            
            // If not found, try to find in system PATH
            if (command_path.empty()) {
                command_path = find_command_in_path(short_path);
            }
        }
        
        // Verify the command path exists
        if (command_path.empty() || !std::filesystem::exists(command_path)) {
            std::cout << "ERROR: Command path does not exist: " << target_path_ << "\n";
            return 3;
        }

        // Show progress message
        std::cout << "Adding " << (global_ ? "global" : "local") << " shim " << shim_name_ << "...\n";
        
        // Create the shim
        if (ShimManager::create_shim(shim_name_, command_path, command_args_, false)) {
            std::cout << "Shim created successfully.\n";
            return 0;
        } else {
            std::cout << "Failed to create shim.\n";
            return 1;
        }
    }
    
    int remove_shim() {
        if (shim_name_.empty()) {
            std::cout << "Shim name is required.\n";
            std::cout << "Usage: sco shim remove <n>\n";
            return 1;
        }
        
        std::cout << "Removing shim '" << shim_name_ << "'...\n";
        
        if (ShimManager::remove_shim(shim_name_)) {
            std::cout << "Shim removed successfully.\n";
            return 0;
        } else {
            std::cout << "Failed to remove shim (may not exist).\n";
            return 1;
        }
    }
    
    int repair_shims() {
        std::cout << "Repairing all shims...\n\n";
        
        auto shims = ShimManager::list_shims();
        
        if (shims.empty()) {
            std::cout << "No shims found to repair.\n";
            return 0;
        }
        
        int repaired = 0;
        int failed = 0;
        
        for (const auto& shim : shims) {
            auto shim_name = shim.name;
            auto target = shim.target_path.string();

            std::cout << "Checking " << shim_name << "... ";

            if (target.empty()) {
                std::cout << "Cannot determine target, skipping\n";
                failed++;
                continue;
            }

            if (!std::filesystem::exists(target)) {
                std::cout << "Target missing, removing shim\n";
                ShimManager::remove_shim(shim_name);
                repaired++;
                continue;
            }

            // Check if shim is working
            if (is_shim_working(shim.shim_path.string())) {
                std::cout << "OK\n";
            } else {
                std::cout << "Broken, recreating... ";

                // Remove and recreate
                ShimManager::remove_shim(shim_name);
                if (ShimManager::create_shim(shim_name, target)) {
                    std::cout << "Fixed\n";
                    repaired++;
                } else {
                    std::cout << "Failed\n";
                    failed++;
                }
            }
        }
        
        std::cout << "\nRepair completed:\n";
        std::cout << "  Repaired: " << repaired << "\n";
        std::cout << "  Failed: " << failed << "\n";
        std::cout << "  Total: " << shims.size() << "\n";
        
        return failed == 0 ? 0 : 1;
    }
    
    std::string get_shim_source(const std::string& target_path) {
        if (target_path.empty()) {
            return "";
        }

        try {
            std::filesystem::path target(target_path);

            // Look for app name in the path
            // Typical Scoop path: C:\Users\<USER>\scoop\apps\appname\current\bin\executable.exe
            // or: C:\ProgramData\scoop\apps\appname\current\executable.exe

            // Convert to string and normalize path separators
            auto path_str = target.string();
            std::replace(path_str.begin(), path_str.end(), '/', '\\');

            // Look for \apps\ pattern
            size_t apps_pos = path_str.find("\\apps\\");
            if (apps_pos != std::string::npos) {
                size_t start = apps_pos + 6; // Length of "\\apps\\"
                size_t end = path_str.find("\\", start);
                if (end != std::string::npos) {
                    return path_str.substr(start, end - start);
                }
            }

            // Alternative approach: walk up the path looking for apps directory
            auto current_path = target.parent_path();
            while (!current_path.empty() && current_path != current_path.parent_path()) {
                if (current_path.parent_path().filename() == "apps") {
                    return current_path.filename().string();
                }
                current_path = current_path.parent_path();
            }

        } catch (const std::exception& e) {
            output::debug("Failed to determine shim source: {}", e.what());
        }

        return "";
    }

    std::string get_shim_target(const std::string& shim_path) {
        try {
            std::ifstream file(shim_path);
            if (!file.is_open()) {
                return "";
            }

            std::string line;
            while (std::getline(file, line)) {
                // Look for common shim patterns
                if (line.find("@\"") != std::string::npos && line.find("\"") != std::string::npos) {
                    // Extract path between quotes
                    size_t start = line.find("@\"") + 2;
                    size_t end = line.find("\"", start);
                    if (end != std::string::npos) {
                        return line.substr(start, end - start);
                    }
                }

                // Look for PowerShell shim pattern
                if (line.find("& \"") != std::string::npos) {
                    size_t start = line.find("& \"") + 3;
                    size_t end = line.find("\"", start);
                    if (end != std::string::npos) {
                        return line.substr(start, end - start);
                    }
                }
            }

        } catch (const std::exception& e) {
            output::debug("Failed to read shim target: {}", e.what());
        }

        return "";
    }
    
    std::string get_shim_status(const std::string& shim_path, const std::string& target) {
        if (target.empty()) {
            return "Unknown target";
        }
        
        if (!std::filesystem::exists(target)) {
            return "Target missing";
        }
        
        if (!is_shim_working(shim_path)) {
            return "Broken";
        }
        
        return "OK";
    }
    
    bool is_shim_working(const std::string& shim_path) {
        // Basic check: shim file exists and is readable
        if (!std::filesystem::exists(shim_path)) {
            return false;
        }
        
        try {
            std::ifstream file(shim_path);
            return file.is_open();
        } catch (const std::exception&) {
            return false;
        }
    }
    
    // Get shim target path by shim name
    std::string get_shim_target_path(const std::string& shim_name) {
        auto& config = Config::instance();
        auto shims_dir = config.get_shims_dir();
        auto shim_path = shims_dir / (shim_name + ".exe");

        if (std::filesystem::exists(shim_path)) {
            return get_shim_target(shim_path.string());
        }
        return "";
    }

    // Find command in system PATH
    std::string find_command_in_path(const std::string& command_name) {
#ifdef _WIN32
        char* path_env = nullptr;
        size_t len = 0;
        if (_dupenv_s(&path_env, &len, "PATH") != 0 || path_env == nullptr) {
            return "";
        }

        std::string path_str(path_env);
        free(path_env);

        std::istringstream path_stream(path_str);
        std::string path_dir;

        while (std::getline(path_stream, path_dir, ';')) {
            if (path_dir.empty()) continue;

            auto exe_path = std::filesystem::path(path_dir) / (command_name + ".exe");
            if (std::filesystem::exists(exe_path)) {
                return exe_path.string();
            }

            auto cmd_path = std::filesystem::path(path_dir) / (command_name + ".cmd");
            if (std::filesystem::exists(cmd_path)) {
                return cmd_path.string();
            }

            auto bat_path = std::filesystem::path(path_dir) / (command_name + ".bat");
            if (std::filesystem::exists(bat_path)) {
                return bat_path.string();
            }
        }
#else
        const char* path_env = getenv("PATH");
        if (!path_env) return "";

        std::string path_str(path_env);
        std::istringstream path_stream(path_str);
        std::string path_dir;

        while (std::getline(path_stream, path_dir, ':')) {
            if (path_dir.empty()) continue;

            auto exe_path = std::filesystem::path(path_dir) / command_name;
            if (std::filesystem::exists(exe_path)) {
                return exe_path.string();
            }
        }
#endif
        return "";
    }
};

} // namespace sco
