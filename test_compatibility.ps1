# Scoop vs SCO Compatibility Test Script
# This script compares scoop and sco commands to verify compatibility

param(
    [string]$TestCategory = "all",
    [switch]$Verbose = $false,
    [switch]$SaveOutput = $true
)

# Configuration
$ScoopPath = "scoop"
$ScoPath = ".\Build\msvc-debug\sco.exe"
$OutputDir = "test_results"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

# Create output directory
if ($SaveOutput) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

function Write-TestHeader {
    param([string]$Title)
    Write-Host "`n" + "="*60 -ForegroundColor Cyan
    Write-Host " $Title" -ForegroundColor Yellow
    Write-Host "="*60 -ForegroundColor Cyan
}

function Write-TestSection {
    param([string]$Section)
    Write-Host "`n--- $Section ---" -ForegroundColor Green
}

function Compare-Commands {
    param(
        [string]$TestName,
        [string]$ScoopCmd,
        [string]$ScoCmd,
        [string]$Description = ""
    )
    
    Write-Host "`n[$TestName] $Description" -ForegroundColor White
    Write-Host "Scoop: $ScoopCmd" -ForegroundColor Gray
    Write-Host "SCO:   $ScoCmd" -ForegroundColor Gray
    
    # Execute scoop command
    Write-Host "`n--- Scoop Output ---" -ForegroundColor Blue
    try {
        $scoopResult = Invoke-Expression $ScoopCmd 2>&1
        $scoopExitCode = $LASTEXITCODE
        Write-Host $scoopResult
        Write-Host "Exit Code: $scoopExitCode" -ForegroundColor DarkGray
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        $scoopResult = "ERROR: $($_.Exception.Message)"
        $scoopExitCode = -1
    }
    
    # Execute sco command
    Write-Host "`n--- SCO Output ---" -ForegroundColor Magenta
    try {
        $scoResult = Invoke-Expression $ScoCmd 2>&1
        $scoExitCode = $LASTEXITCODE
        Write-Host $scoResult
        Write-Host "Exit Code: $scoExitCode" -ForegroundColor DarkGray
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        $scoResult = "ERROR: $($_.Exception.Message)"
        $scoExitCode = -1
    }
    
    # Save results if requested
    if ($SaveOutput) {
        $resultFile = "$OutputDir\${TestName}_${Timestamp}.txt"
        @"
Test: $TestName
Description: $Description
Timestamp: $(Get-Date)

Scoop Command: $ScoopCmd
Scoop Exit Code: $scoopExitCode
Scoop Output:
$scoopResult

SCO Command: $ScoCmd
SCO Exit Code: $scoExitCode
SCO Output:
$scoResult

"@ | Out-File -FilePath $resultFile -Encoding UTF8
    }
    
    # Compare results
    if ($scoopExitCode -eq $scoExitCode) {
        Write-Host "`n✓ Exit codes match" -ForegroundColor Green
    } else {
        Write-Host "`n✗ Exit codes differ (Scoop: $scoopExitCode, SCO: $scoExitCode)" -ForegroundColor Red
    }
    
    Write-Host "-" * 50 -ForegroundColor DarkGray
}

function Test-BasicCommands {
    Write-TestHeader "基础命令测试 (Basic Commands)"
    
    Write-TestSection "帮助和版本信息"
    Compare-Commands "help" "$ScoopPath help" "$ScoPath help" "显示帮助信息"
    Compare-Commands "version" "$ScoopPath --version" "$ScoPath --version" "显示版本信息"
    Compare-Commands "help_specific" "$ScoopPath help install" "$ScoPath help install" "显示特定命令帮助"
    
    Write-TestSection "无效命令测试"
    Compare-Commands "invalid_command" "$ScoopPath invalidcmd" "$ScoPath invalidcmd" "测试无效命令处理"
    Compare-Commands "no_args" "$ScoopPath" "$ScoPath" "无参数调用"
}

function Test-InfoCommands {
    Write-TestHeader "信息查询命令测试 (Information Commands)"
    
    Write-TestSection "状态和列表"
    Compare-Commands "status" "$ScoopPath status" "$ScoPath status" "显示系统状态"
    Compare-Commands "list" "$ScoopPath list" "$ScoPath list" "列出已安装应用"
    Compare-Commands "list_verbose" "$ScoopPath list --verbose" "$ScoPath list --verbose" "详细列表"
    
    Write-TestSection "搜索功能"
    Compare-Commands "search_git" "$ScoopPath search git" "$ScoPath search git" "搜索git应用"
    Compare-Commands "search_nonexistent" "$ScoopPath search nonexistentapp123" "$ScoPath search nonexistentapp123" "搜索不存在的应用"
    
    Write-TestSection "应用信息"
    Compare-Commands "info_git" "$ScoopPath info git" "$ScoPath info git" "显示git应用信息"
    Compare-Commands "info_nonexistent" "$ScoopPath info nonexistentapp123" "$ScoPath info nonexistentapp123" "查询不存在应用信息"
    
    Write-TestSection "路径和定位"
    Compare-Commands "which_git" "$ScoopPath which git" "$ScoPath which git" "定位git可执行文件"
    Compare-Commands "prefix_git" "$ScoopPath prefix git" "$ScoPath prefix git" "获取git安装路径"
}

function Test-ConfigCommands {
    Write-TestHeader "配置管理测试 (Configuration Commands)"
    
    Write-TestSection "配置查看"
    Compare-Commands "config_list" "$ScoopPath config" "$ScoPath config" "列出所有配置"
    Compare-Commands "config_get_root" "$ScoopPath config root_path" "$ScoPath config root_path" "获取root_path配置"
    
    Write-TestSection "缓存管理"
    Compare-Commands "cache_show" "$ScoopPath cache show" "$ScoPath cache show" "显示缓存信息"
    Compare-Commands "cache_list" "$ScoopPath cache" "$ScoPath cache" "列出缓存"
}

function Test-BucketCommands {
    Write-TestHeader "Bucket 管理测试 (Bucket Management)"
    
    Write-TestSection "Bucket 操作"
    Compare-Commands "bucket_list" "$ScoopPath bucket list" "$ScoPath bucket list" "列出已添加的bucket"
    Compare-Commands "bucket_known" "$ScoopPath bucket known" "$ScoPath bucket known" "列出已知bucket"
}

function Test-AdvancedCommands {
    Write-TestHeader "高级功能测试 (Advanced Commands)"
    
    Write-TestSection "依赖和检查"
    Compare-Commands "checkup" "$ScoopPath checkup" "$ScoPath checkup" "系统检查"
    Compare-Commands "depends_git" "$ScoopPath depends git" "$ScoPath depends git" "查看git依赖"
    
    Write-TestSection "导出导入"
    Compare-Commands "export" "$ScoopPath export" "$ScoPath export" "导出已安装应用"
}

function Test-ShimCommands {
    Write-TestHeader "Shim 管理测试 (Shim Management)"
    
    Write-TestSection "Shim 操作"
    Compare-Commands "shim_list" "$ScoopPath shim list" "$ScoPath shim list" "列出所有shim"
    Compare-Commands "shim_info" "$ScoopPath shim" "$ScoPath shim" "显示shim帮助"
}

function Test-ErrorHandling {
    Write-TestHeader "错误处理测试 (Error Handling)"
    
    Write-TestSection "参数错误"
    Compare-Commands "install_no_args" "$ScoopPath install" "$ScoPath install" "install命令无参数"
    Compare-Commands "uninstall_no_args" "$ScoopPath uninstall" "$ScoPath uninstall" "uninstall命令无参数"
    Compare-Commands "info_no_args" "$ScoopPath info" "$ScoPath info" "info命令无参数"
    
    Write-TestSection "权限和路径错误"
    Compare-Commands "install_invalid" "$ScoopPath install invalidapp123" "$ScoPath install invalidapp123" "安装不存在的应用"
}

# Main execution
Write-Host "Scoop vs SCO Compatibility Test" -ForegroundColor Cyan
Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Gray
Write-Host "Scoop Path: $ScoopPath" -ForegroundColor Gray
Write-Host "SCO Path: $ScoPath" -ForegroundColor Gray

if ($SaveOutput) {
    Write-Host "Results will be saved to: $OutputDir" -ForegroundColor Gray
}

# Check if both executables exist
try {
    & $ScoopPath --version | Out-Null
    Write-Host "✓ Scoop found" -ForegroundColor Green
} catch {
    Write-Host "✗ Scoop not found or not working" -ForegroundColor Red
    exit 1
}

if (Test-Path $ScoPath) {
    Write-Host "✓ SCO found" -ForegroundColor Green
} else {
    Write-Host "✗ SCO not found at $ScoPath" -ForegroundColor Red
    exit 1
}

# Run tests based on category
switch ($TestCategory.ToLower()) {
    "basic" { Test-BasicCommands }
    "info" { Test-InfoCommands }
    "config" { Test-ConfigCommands }
    "bucket" { Test-BucketCommands }
    "advanced" { Test-AdvancedCommands }
    "shim" { Test-ShimCommands }
    "error" { Test-ErrorHandling }
    "all" {
        Test-BasicCommands
        Test-InfoCommands
        Test-ConfigCommands
        Test-BucketCommands
        Test-AdvancedCommands
        Test-ShimCommands
        Test-ErrorHandling
    }
    default {
        Write-Host "Invalid test category. Available: basic, info, config, bucket, advanced, shim, error, all" -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n" + "="*60 -ForegroundColor Cyan
Write-Host " 测试完成 (Testing Complete)" -ForegroundColor Yellow
Write-Host "="*60 -ForegroundColor Cyan

if ($SaveOutput) {
    Write-Host "`nTest results saved to: $OutputDir" -ForegroundColor Green
}
