#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/install_manager.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <vector>
#include <string>
#include <cstdlib>
#include <nlohmann/json.hpp>
#include "../utils/output.hpp"

namespace sco {

class ImportCommand : public BaseCommand {
public:
    ImportCommand() = default;
    
    int execute() override {
        try {
            if (import_file_.empty()) {
                std::cerr << "Import file is required.\n";
                std::cout << "Usage: sco import <file.json>\n";
                return 1;
            }

            output::debug("Import command called with file: {}", import_file_);

            return import_from_file();

        } catch (const std::exception& e) {
            output::error("Import command failed: {}", e.what());
            std::cerr << "Import failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "import"; }
    std::string get_description() const override { return "Imports apps, buckets and configs from a Scoopfile in JSON format"; }
    
    void set_import_file(const std::string& file) { import_file_ = file; }
    void set_skip_installed(bool skip) { skip_installed_ = skip; }
    void set_skip_buckets(bool skip) { skip_buckets_ = skip; }
    void set_skip_config(bool skip) { skip_config_ = skip; }
    
private:
    std::string import_file_;
    bool skip_installed_ = false;
    bool skip_buckets_ = false;
    bool skip_config_ = false;
    
    int import_from_file() {
        // Check if file exists
        if (!std::filesystem::exists(import_file_)) {
            std::cerr << "Import file not found: " << import_file_ << std::endl;
            return 1;
        }
        
        // Read and parse JSON file
        nlohmann::json import_data;
        try {
            std::ifstream file(import_file_);
            file >> import_data;
        } catch (const std::exception& e) {
            std::cerr << "Failed to parse import file: " << e.what() << std::endl;
            return 1;
        }
        
        std::cout << "Importing from: " << import_file_ << std::endl;
        
        // Show import summary
        show_import_summary(import_data);
        
        bool all_success = true;
        
        // Import buckets first
        if (!skip_buckets_ && import_data.contains("buckets")) {
            if (!import_buckets(import_data["buckets"])) {
                all_success = false;
            }
        }
        
        // Import configuration
        if (!skip_config_ && import_data.contains("config")) {
            if (!import_config(import_data["config"])) {
                all_success = false;
            }
        }
        
        // Import apps
        if (!skip_installed_ && import_data.contains("apps")) {
            if (!import_apps(import_data["apps"])) {
                all_success = false;
            }
        }
        
        if (all_success) {
            std::cout << "\nImport completed successfully.\n";
        } else {
            std::cout << "\nImport completed with some errors.\n";
        }
        
        return all_success ? 0 : 1;
    }
    
    void show_import_summary(const nlohmann::json& import_data) {
        std::cout << "\nImport Summary:\n";
        std::cout << "==============\n";
        
        if (import_data.contains("version")) {
            std::cout << "Export version: " << import_data["version"].get<std::string>() << "\n";
        }
        
        if (import_data.contains("exported_at")) {
            std::cout << "Exported at: " << import_data["exported_at"].get<std::string>() << "\n";
        }
        
        if (import_data.contains("buckets") && !skip_buckets_) {
            auto buckets = import_data["buckets"];
            std::cout << "Buckets to import: " << buckets.size() << "\n";
        }
        
        if (import_data.contains("apps") && !skip_installed_) {
            auto apps = import_data["apps"];
            std::cout << "Apps to import: " << apps.size() << "\n";
        }
        
        if (import_data.contains("config") && !skip_config_) {
            std::cout << "Configuration: Yes\n";
        }
        
        std::cout << "\n";
    }
    
    bool import_buckets(const nlohmann::json& buckets) {
        std::cout << "Importing buckets...\n";
        
        bool all_success = true;
        
        for (const auto& bucket : buckets) {
            if (!bucket.contains("name")) {
                output::warn("Bucket entry missing name, skipping");
                continue;
            }
            
            std::string bucket_name = bucket["name"];
            std::string bucket_source;
            
            if (bucket.contains("source") && bucket["source"] != "local") {
                bucket_source = bucket["source"];
            }
            
            std::cout << "  Adding bucket: " << bucket_name;
            if (!bucket_source.empty()) {
                std::cout << " (" << bucket_source << ")";
            }
            std::cout << "... ";
            
            if (add_bucket(bucket_name, bucket_source)) {
                std::cout << "OK\n";
            } else {
                std::cout << "Failed\n";
                all_success = false;
            }
        }
        
        return all_success;
    }
    
    bool add_bucket(const std::string& bucket_name, const std::string& bucket_source) {
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        auto bucket_dir = buckets_dir / bucket_name;
        
        // Check if bucket already exists
        if (std::filesystem::exists(bucket_dir)) {
            output::debug("Bucket {} already exists, skipping", bucket_name);
            return true;
        }
        
        if (bucket_source.empty()) {
            output::warn("No source URL for bucket {}, skipping", bucket_name);
            return false;
        }
        
        // Create buckets directory if it doesn't exist
        std::filesystem::create_directories(buckets_dir);
        
        // Clone the bucket repository
        std::string command = "git clone \"" + bucket_source + "\" \"" + bucket_dir.string() + "\"";
        output::debug("Executing: {}", command);
        
        int result = std::system(command.c_str());
        
        if (result != 0) {
            // Clean up failed clone
            if (std::filesystem::exists(bucket_dir)) {
                try {
                    std::filesystem::remove_all(bucket_dir);
                } catch (const std::exception& e) {
                    output::warn("Failed to clean up failed bucket clone: {}", e.what());
                }
            }
            return false;
        }
        
        return true;
    }
    
    bool import_config(const nlohmann::json& config_data) {
        std::cout << "Importing configuration...\n";
        
        if (!config_data.contains("user_config")) {
            std::cout << "  No user configuration to import.\n";
            return true;
        }
        
        try {
            auto& config = Config::instance();
            auto config_file = config.get_config_file();
            
            // Create config directory if it doesn't exist
            std::filesystem::create_directories(config_file.parent_path());
            
            // Write user configuration
            std::ofstream file(config_file);
            if (!file.is_open()) {
                std::cout << "  Failed to open config file for writing.\n";
                return false;
            }
            
            file << config_data["user_config"].dump(2);
            file.close();
            
            std::cout << "  Configuration imported successfully.\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "  Failed to import configuration: " << e.what() << "\n";
            return false;
        }
    }
    
    bool import_apps(const nlohmann::json& apps) {
        std::cout << "Importing apps...\n";
        
        std::vector<std::string> apps_to_install;
        std::vector<std::string> global_apps_to_install;
        
        // Separate local and global apps
        for (const auto& app : apps) {
            if (!app.contains("name")) {
                output::warn("App entry missing name, skipping");
                continue;
            }
            
            std::string app_name = app["name"];
            
            // Check if app is already installed
            if (is_app_installed(app_name)) {
                std::cout << "  " << app_name << " is already installed, skipping.\n";
                continue;
            }
            
            bool is_global = app.contains("global") && app["global"].get<bool>();
            
            if (is_global) {
                global_apps_to_install.push_back(app_name);
            } else {
                apps_to_install.push_back(app_name);
            }
        }
        
        bool all_success = true;
        
        // Install local apps
        if (!apps_to_install.empty()) {
            std::cout << "  Installing local apps: ";
            for (size_t i = 0; i < apps_to_install.size(); ++i) {
                std::cout << apps_to_install[i];
                if (i < apps_to_install.size() - 1) std::cout << ", ";
            }
            std::cout << "\n";
            
            InstallManager::InstallOptions options;
            options.global = false;
            
            auto result = InstallManager::install_apps(apps_to_install, options);
            if (!result.success) {
                std::cout << "  Some local apps failed to install.\n";
                all_success = false;
            }
        }
        
        // Install global apps
        if (!global_apps_to_install.empty()) {
            std::cout << "  Installing global apps: ";
            for (size_t i = 0; i < global_apps_to_install.size(); ++i) {
                std::cout << global_apps_to_install[i];
                if (i < global_apps_to_install.size() - 1) std::cout << ", ";
            }
            std::cout << "\n";
            
            InstallManager::InstallOptions options;
            options.global = true;
            
            auto result = InstallManager::install_apps(global_apps_to_install, options);
            if (!result.success) {
                std::cout << "  Some global apps failed to install.\n";
                all_success = false;
            }
        }
        
        return all_success;
    }
    
    bool is_app_installed(const std::string& app_name) {
        auto& config = Config::instance();
        
        // Check local installation
        auto local_app_dir = config.get_apps_dir() / app_name;
        if (std::filesystem::exists(local_app_dir)) {
            return true;
        }
        
        // Check global installation
        auto global_app_dir = config.get_global_apps_dir() / app_name;
        if (std::filesystem::exists(global_app_dir)) {
            return true;
        }
        
        return false;
    }
};

} // namespace sco
