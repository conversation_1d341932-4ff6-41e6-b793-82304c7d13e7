# SCO 兼容性问题报告

## 问题概述
在对比测试 Scoop 和 SCO 的兼容性过程中，发现了几个需要立即修复的严重问题。

## 严重问题 (Critical Issues)

### 1. bucket list 命令功能错误
**问题描述**: SCO 的 `bucket list` 命令输出应用列表而不是 bucket 列表

**期望行为**:
```
scoop bucket list
Name   Source                                      Updated               Manifests
----   ------                                      -------               ---------
main   https://github.com/ScoopInstaller/Main.git  6/23/2025 12:32:15 PM      1391
extras https://github.com/ScoopInstaller/Extras    6/23/2025 12:31:36 PM      2167
spc    https://gitee.com/wlzwme/scoop-proxy-cn.git 6/23/2025 10:24:50 AM     10429
```

**实际行为**:
```
sco bucket list
Installed apps:

----- -------- ------ ------------------- ----
Name  Version  Source Updated             Info
----- -------- ------ ------------------- ----
7zip  24.09    main   2025-06-23 15:54:04     
aria2 1.37.0-1 main   2025-06-23 14:25:15     
```

**影响**: 用户无法管理 bucket，这是 Scoop 的核心功能之一

### 2. shim list 命令功能错误
**问题描述**: SCO 的 `shim list` 命令输出应用列表而不是 shim 列表

**期望行为**:
```
scoop shim list
Name   Source   Alternatives IsGlobal IsHidden
----   ------   ------------ -------- --------
aria2c aria2                 False    False   
not2   notepad2              False    False   
scoop  scoop                 False    False   
```

**实际行为**:
```
sco shim list
Installed apps:

----- -------- ------ ------------------- ----
Name  Version  Source Updated             Info
----- -------- ------ ------------------- ----
7zip  24.09    main   2025-06-23 15:54:04     
aria2 1.37.0-1 main   2025-06-23 14:25:15     
```

**影响**: 用户无法管理 shim，这会影响可执行文件的管理

### 3. manifest 解析错误
**问题描述**: SCO 无法解析包含数组类型 notes 字段的 manifest 文件

**错误信息**:
```
sco info git
✗ Error parsing manifest C:\Users\<USER>\scoop\buckets\main\bucket\git.json: [json.exception.type_error.302] type must be string, but is array
```

**问题分析**: 
- git manifest 中的 `notes` 字段是数组类型
- SCO 的 JSON 解析器期望字符串类型
- 这导致无法获取许多应用的信息

**影响**: 用户无法查看和安装许多重要应用(如 git)

## 可能的原因分析

### 命令路由问题
bucket list 和 shim list 都输出相同的应用列表，可能的原因：
1. 命令注册时路由到了错误的处理函数
2. 命令解析器没有正确识别子命令
3. 默认行为被设置为显示应用列表

### JSON 解析器问题
manifest 解析失败的原因：
1. JSON 解析器对字段类型过于严格
2. 没有处理 Scoop manifest 的多种字段类型
3. 缺少对数组类型字段的支持

## 建议的修复方案

### 1. 修复命令路由
检查以下文件中的命令注册和路由逻辑：
- `src/commands/command_manager.hpp`
- `src/commands/bucket_command.hpp`
- `src/commands/shim_command.hpp`

确保每个命令都正确路由到对应的处理函数。

### 2. 改进 JSON 解析
修改 manifest 解析器以支持：
- 字符串和数组类型的 notes 字段
- 其他可能的字段类型变化
- 更宽松的类型检查

### 3. 添加测试用例
为这些命令添加单元测试，确保：
- bucket 命令正确处理 bucket 操作
- shim 命令正确处理 shim 操作
- manifest 解析器能处理各种字段类型

## 测试验证

### 验证 bucket 命令修复
```bash
sco bucket list        # 应显示 bucket 列表
sco bucket known       # 应显示已知 bucket
```

### 验证 shim 命令修复
```bash
sco shim list          # 应显示 shim 列表
sco shim               # 应显示 shim 帮助
```

### 验证 manifest 解析修复
```bash
sco info git           # 应正确显示 git 信息
sco info 7zip          # 应继续正常工作
```

## 优先级
1. **高优先级**: 修复 bucket 和 shim 命令路由问题
2. **高优先级**: 修复 manifest 解析器
3. **中优先级**: 添加相关测试用例
4. **低优先级**: 优化输出格式一致性

## 影响评估
这些问题严重影响 SCO 的可用性，用户无法：
- 管理软件源 (bucket)
- 管理可执行文件链接 (shim)
- 获取重要应用信息 (git 等)

建议在发布前必须修复这些问题，否则会严重影响用户体验和 Scoop 兼容性目标。
